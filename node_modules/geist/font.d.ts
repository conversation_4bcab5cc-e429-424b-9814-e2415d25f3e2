import { NextFontWithVariable } from "next/dist/compiled/@next/font";

declare module "geist/font" {
  /**
   * @deprecated - Import from `geist/font/sans` instead.
   *
   * Geist Sans variable font, with `className` and `variable` properties,
   * meant to be attached to DOM elements via `className`
   *
   * `GeistSans`—approximately 30kb—is preferred in almost all cases. Use `GeistSansNonVariable`—approximately
   * 300kb—if you need to support browsers that {@link https://caniuse.com/variable-fonts cannot display variable fonts}
   *
   * Included weights: 100 through 900
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#app-router View App Router Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#with-tailwind-css View Tailwind Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#pages-router View Pages Router Example}
   *
   * * {@link https://github.com/vercel/geist-font/releases Download Font Files}
   */
  export const GeistSans: NextFontWithVariable;

  /**
   * @deprecated - Import from `geist/font/sans-non-variable` instead.
   *
   * Geist Sans font, with `className` and `variable` properties,
   * meant to be attached to DOM elements via `className`
   *
   * `GeistSans`—approximately 30kb—is preferred in almost all cases. Use `GeistSansNonVariable`—approximately
   * 300kb—if you need to support browsers that {@link https://caniuse.com/variable-fonts cannot display variable fonts}
   *
   * Included weights: 100 through 900
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#app-router View App Router Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#with-tailwind-css View Tailwind Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#pages-router View Pages Router Example}
   *
   * * {@link https://github.com/vercel/geist-font/releases Download Font Files}
   * @deprecated Use `GeistSans` instead
   */
  export const GeistSansNonVariable: NextFontWithVariable;

  /**
   * @deprecated - Import from `geist/font/mono` instead.
   *
   * Geist Mono variable font, with `className` and `variable` properties,
   * meant to be attached to DOM elements via `className`
   *
   * `GeistMono`—approximately 30kb—is preferred in almost all cases. Use `GeistMonoNonVariable`—approximately
   * 300kb—if you need to support browsers that {@link https://caniuse.com/variable-fonts cannot display variable fonts}
   *
   * Included weights: 100 through 900.
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#app-router View App Router Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#with-tailwind-css View Tailwind Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#pages-router View Pages Router Example}
   *
   * * {@link https://github.com/vercel/geist-font/releases Download Font Files}
   */
  export const GeistMono: NextFontWithVariable;

  /**
   * @deprecated - Import from `geist/font/mono-non-variable` instead.
   *
   * Geist Mono font, with `className` and `variable` properties,
   * meant to be attached to DOM elements via `className`
   *
   * `GeistMono`—approximately 30kb—is preferred in almost all cases. Use `GeistMonoNonVariable`—approximately
   * 300kb—if you need to support browsers that {@link https://caniuse.com/variable-fonts cannot display variable fonts}
   *
   * Included weights: 100 through 900.
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#app-router View App Router Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#with-tailwind-css View Tailwind Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#pages-router View Pages Router Example}
   *
   * * {@link https://github.com/vercel/geist-font/releases Download Font Files}
   */
  export const GeistMonoNonVariable: NextFontWithVariable;
}

declare module "geist/font/mono" {
  /**
   * Geist Mono variable font, with `className` and `variable` properties,
   * meant to be attached to DOM elements via `className`
   *
   * `GeistMono`—approximately 30kb—is preferred in almost all cases. Use `GeistMonoNonVariable`—approximately
   * 300kb—if you need to support browsers that {@link https://caniuse.com/variable-fonts cannot display variable fonts}
   *
   * Included weights: 100 through 900.
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#app-router View App Router Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#with-tailwind-css View Tailwind Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#pages-router View Pages Router Example}
   *
   * * {@link https://github.com/vercel/geist-font/releases Download Font Files}
   */
  export const GeistMono: NextFontWithVariable;
}

declare module "geist/font/mono-non-variable" {
  /**
   * Geist Mono font, with `className` and `variable` properties,
   * meant to be attached to DOM elements via `className`
   *
   * `GeistMono`—approximately 30kb—is preferred in almost all cases. Use `GeistMonoNonVariable`—approximately
   * 300kb—if you need to support browsers that {@link https://caniuse.com/variable-fonts cannot display variable fonts}
   *
   * Included weights: 100 through 900.
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#app-router View App Router Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#with-tailwind-css View Tailwind Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#pages-router View Pages Router Example}
   *
   * * {@link https://github.com/vercel/geist-font/releases Download Font Files}
   */
  export const GeistMonoNonVariable: NextFontWithVariable;
}

declare module "geist/font/sans" {
  /**
   * Geist Sans font, with `className` and `variable` properties,
   * meant to be attached to DOM elements via `className`
   *
   * `GeistSans`—approximately 30kb—is preferred in almost all cases. Use `GeistSansNonVariable`—approximately
   * 300kb—if you need to support browsers that {@link https://caniuse.com/variable-fonts cannot display variable fonts}
   *
   * Included weights: 100 through 900
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#app-router View App Router Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#with-tailwind-css View Tailwind Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#pages-router View Pages Router Example}
   *
   * * {@link https://github.com/vercel/geist-font/releases Download Font Files}
   */
  export const GeistSans: NextFontWithVariable;
}

declare module "geist/font/sans-non-variable" {
  /**
   * Geist Sans font, with `className` and `variable` properties,
   * meant to be attached to DOM elements via `className`
   *
   * `GeistSans`—approximately 30kb—is preferred in almost all cases. Use `GeistSansNonVariable`—approximately
   * 300kb—if you need to support browsers that {@link https://caniuse.com/variable-fonts cannot display variable fonts}
   *
   * Included weights: 100 through 900.
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#app-router View App Router Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#with-tailwind-css View Tailwind Example}
   *
   * * {@link https://www.npmjs.com/package/geist?activeTab=readme#pages-router View Pages Router Example}
   *
   * * {@link https://github.com/vercel/geist-font/releases Download Font Files}
   */
  export const GeistSansNonVariable: NextFontWithVariable;
}
