{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/globals.css"], "sourcesContent": [":root {\n  --background: #ffffff;\n  --foreground: #171717;\n  --accent-color: #007bff; /* Clean Blue Accent Color */\n}\n\n@media (prefers-color-scheme: dark) {\n  :root {\n    --background: #0a0a0a;\n    --foreground: #ededed;\n  }\n}\n\nhtml,\nbody {\n  max-width: 100vw;\n  overflow-x: hidden;\n}\n\nbody {\n  color: var(--foreground);\n  background: var(--background);\n  font-family: \"SF Pro Rounded\", \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, \"Open Sans\", \"Helvetica Neue\", sans-serif; /* SF Pro Rounded with fallbacks */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n* {\n  box-sizing: border-box;\n  padding: 0;\n  margin: 0;\n}\n\na {\n  color: inherit;\n  text-decoration: none;\n}\n\n@media (prefers-color-scheme: dark) {\n  html {\n    color-scheme: dark;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;EACE;;;;;;AAMF;;;;;AAMA;;;;;;;;AAQA;;;;;;AAMA;;;;;AAKA;EACE"}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}