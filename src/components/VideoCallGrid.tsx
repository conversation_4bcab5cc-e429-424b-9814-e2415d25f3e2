'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useRoom, useLocalParticipant, useParticipantIds, useParticipant, DailyVideo } from '@daily-co/daily-react';
import MeetingControls from './MeetingControls';

// Using a more neutral placeholder for a white theme
const EXIE_VIDEO_PLACEHOLDER = 'https://via.placeholder.com/320x240.png?text=Exie+AI';

// Define props interface for VideoCallGrid
interface VideoCallGridProps {
  // State and handlers controlled by parent (MeetingPage)
  joined: boolean;
  micOn: boolean;
  cameraOn: boolean;
  exieLoading: boolean;
  exieVideoUrl: string | null;
  exieTranscript: string;
  // Callbacks to communicate user actions to parent
  onToggleMic: () => void;
  onToggleCamera: () => void;
  onLeave: () => void;
  onTalkToExie: () => void; // Exie interaction handler from parent
}

// Update component signature to accept props
const VideoCallGrid: React.FC<VideoCallGridProps> = ({
  joined,
  micOn,
  cameraOn,
  exieLoading,
  exieVideoUrl,
  exieTranscript,
  onToggleMic,
  onToggleCamera,
  onLeave,
  onTalkToExie,
}) => {
  const room = useRoom();
  const localParticipant = useLocalParticipant();

  // Get IDs of all participants, excluding the local participant
  const participantIds = useParticipantIds();
  const remoteParticipantIds = participantIds.filter(id => id !== localParticipant?.session_id);

  // Handlers for controls - these just call the parent-provided handlers
  // These are not directly used in this component's render but are available if needed.
  // const handleToggleMicClick = () => { onToggleMic(); };
  // const handleToggleCameraClick = () => { onToggleCamera(); };
  // const handleLeaveClick = async () => { onLeave(); };


  return (
    <div style={{
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', // Responsive grid for videos
      gap: 16,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 16,
      backgroundColor: '#ffffff', // White background for the grid area
      borderRadius: 8,
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      flexGrow: 1, // Allow the grid to take available space
    }}>
      {/* Exie AI video */}
      <div key="exie-ai" style={{
        border: '1px solid rgba(0, 123, 255, 0.3)', // Accent color border with low opacity
        borderRadius: 8,
        overflow: 'hidden',
        width: '100%',
        paddingTop: '75%', // Maintain 4:3 aspect ratio
        position: 'relative',
        background: '#eee',
        display: 'flex', // Use flex for centering loading text
        flexDirection: 'column',
      }}>
        {exieLoading ? (
          <div style={{ color: '#555', textAlign: 'center', margin: 'auto' }}>Exie is thinking...</div> // Darker text
        ) : exieVideoUrl ? (
          // Use controls only for debugging, remove for final version
          <video src={exieVideoUrl} autoPlay playsInline style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', objectFit: 'cover' }} />
        ) : (
          <img src={EXIE_VIDEO_PLACEHOLDER} alt="Exie AI" style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
        )}
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          textAlign: 'center',
          background: 'rgba(255,255,255,0.7)', // Semi-transparent white label
          color: '#333', // Darker text
          fontWeight: 600,
          padding: '4px 0',
        }}>Exie (AI)</div>
        {exieTranscript && (
          <div style={{
            position: 'absolute',
            top: 0, // Position transcript at the top for less overlap with label
            left: 0,
            right: 0,
            background: 'rgba(0,0,0,0.7)', // Darker background for readability on any video
            color: '#fff', // White text
            padding: 8,
            fontSize: 14,
            maxHeight: '50%', // Limit transcript height
            overflowY: 'auto',
          }}>{exieTranscript}</div>
        )}
      </div>

      {/* Local participant video */}
      {localParticipant && (
        <div key={localParticipant.session_id} style={{
          border: '1px solid rgba(0, 123, 255, 0.5)', // Slightly more opaque accent border for local
          borderRadius: 8,
          overflow: 'hidden',
          width: '100%',
          paddingTop: '75%',
          position: 'relative',
          background: '#eee',
        }}>
          {/* Use DailyVideo component for local participant */}
          {localParticipant.video ? (
            <DailyVideo
              sessionId={localParticipant.session_id}
              type="video"
              autoPlay
              // The DailyVideo component handles attaching the track internally
              style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', objectFit: 'cover' }}
            />
          ) : ( // Placeholder when camera is off or track not ready
            <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', color: '#555' }}>Camera Off</div>
          )}
          <div style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            textAlign: 'center',
            background: 'rgba(255,255,255,0.7)',
            color: 'var(--accent-color)', // Use accent color for 'You' label
            fontWeight: 600,
            padding: '4px 0',
          }}>You</div>
        </div>
      )}

      {/* Remote participants video */}
      {remoteParticipantIds.map(id => {
        return <RemoteParticipantVideo key={id} participantId={id} />;
      })}
    </div>
  );
};

// Helper component to render remote participant video
const RemoteParticipantVideo: React.FC<{ participantId: string }> = ({ participantId }) => {
  const participant = useParticipant(participantId);

  // Use DailyVideo component for remote participants
  if (!participant || !participant.video) {
    return null; // Explicitly return null if no video to render or if track is boolean (e.g., false)
  }

  return (
    <div key={participant.session_id} style={{
      border: '1px solid rgba(0, 123, 255, 0.3)', // Accent color border with low opacity
      borderRadius: 8,
      overflow: 'hidden',
      width: '100%',
      paddingTop: '75%',
      position: 'relative',
      background: '#eee',
    }}>
      <DailyVideo
        sessionId={participant.session_id}
        type="video"
        autoPlay
        style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', objectFit: 'cover' }}
      />
      <div style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        textAlign: 'center',
        background: 'rgba(255,255,255,0.7)',
        color: '#333',
        fontWeight: 600,
        padding: '4px 0',
      }}>User {participant.user_name || participant.session_id.substring(0, 8)}</div> {/* Display participant name or truncated session ID */}
    </div>
  );
};

export default VideoCallGrid; 