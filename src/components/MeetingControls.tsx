import React from 'react';

interface MeetingControlsProps {
  micOn: boolean;
  cameraOn: boolean;
  onToggleMic: () => void;
  onToggleCamera: () => void;
  onLeave: () => void;
  onTalkToExie: () => void;
  exieLoading: boolean;
}

const MeetingControls: React.FC<MeetingControlsProps> = ({
  micOn,
  cameraOn,
  onToggleMic,
  onToggleCamera,
  onLeave,
  onTalkToExie,
  exieLoading,
}) => {
  return (
    <div style={{
      display: 'flex',
      gap: '16px',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '16px 24px',
      backgroundColor: '#fff',
      borderRadius: '8px',
      boxShadow: '0 4px 8px rgba(0,0,0,0.05)',
      margin: '16px 0',
    }}>
      <button onClick={onToggleMic} style={{ padding: 12, borderRadius: '50%', background: micOn ? '#0070f3' : '#ccc', color: '#fff', border: 'none', fontSize: 20 }}>
        {micOn ? '🎤' : '🔇'}
      </button>
      <button onClick={onToggleCamera} style={{ padding: 12, borderRadius: '50%', background: cameraOn ? '#0070f3' : '#ccc', color: '#fff', border: 'none', fontSize: 20 }}>
        {cameraOn ? '📷' : '🚫'}
      </button>
      <button
        onClick={onTalkToExie}
        disabled={exieLoading}
        style={{
          backgroundColor: exieLoading ? '#ccc' : 'var(--accent-color)',
          color: '#fff',
          padding: '12px 24px',
          borderRadius: '4px',
          border: 'none',
          cursor: exieLoading ? 'not-allowed' : 'pointer',
          fontSize: '16px',
          fontWeight: 600,
        }}
      >
        {exieLoading ? 'Exie is thinking...' : 'Talk to Exie'}
      </button>
      <button onClick={onLeave} style={{ padding: 12, borderRadius: '50%', background: '#e74c3c', color: '#fff', border: 'none', fontSize: 20 }}>
        🚪
      </button>
    </div>
  );
};

export default MeetingControls; 