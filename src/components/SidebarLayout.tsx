import Link from 'next/link';
import React from 'react';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  return (
    <div style={{ display: 'flex' }}>
      {/* Sidebar */}
      <aside style={{
        width: '200px',
        backgroundColor: '#f0f0f0', // Light grey background for sidebar
        padding: '16px',
        minHeight: '100vh',
      }}>
        <nav>
          <ul style={{ listStyle: 'none', padding: 0 }}>
            <li style={{ marginBottom: '16px' }}>
              <Link href="/" style={{ color: 'var(--accent-color)', textDecoration: 'none' }}>Home</Link>
            </li>
            <li style={{ marginBottom: '16px' }}>
              <Link href="/dashboard" style={{ color: 'var(--accent-color)', textDecoration: 'none' }}>Dashboard</Link>
            </li>
            <li style={{ marginBottom: '16px' }}>
              <Link href="/tweet-center" style={{ color: 'var(--accent-color)', textDecoration: 'none' }}>Tweet Center</Link>
            </li>
            <li style={{ marginBottom: '16px' }}>
              <Link href="/meeting" style={{ color: 'var(--accent-color)', textDecoration: 'none' }}>AI Meeting</Link>
            </li>
          </ul>
        </nav>
      </aside>

      {/* Main Content */}
      <main style={{ flexGrow: 1, padding: '24px' }}>
        {children}
      </main>
    </div>
  );
};

export default SidebarLayout; 