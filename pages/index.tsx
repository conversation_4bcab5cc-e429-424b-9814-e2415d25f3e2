// pages/index.tsx
import Link from 'next/link';
import React from 'react';
import SidebarLayout from '../src/components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const HomePage: NextPageWithLayout = () => {
  return (
    <div style={{ padding: '24px' }}>
      <h1 style={{ color: 'var(--accent-color)', marginBottom: '24px' }}>Welcome to Exie AI</h1>
      <p style={{ marginBottom: '16px' }}>Select a page from the sidebar.</p>
      <Link href="/meeting" style={{ color: 'var(--accent-color)', textDecoration: 'none' }}>Go to Meeting Page</Link>
    </div>
  );
};

HomePage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default HomePage; 