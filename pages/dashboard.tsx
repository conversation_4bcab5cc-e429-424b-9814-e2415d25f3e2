// pages/dashboard.tsx
import React from 'react';
import SidebarLayout from '../src/components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const DashboardPage: NextPageWithLayout = () => {
  return (
    <div style={{ padding: '24px' }}>
      <h1 style={{ color: 'var(--accent-color)', marginBottom: '24px' }}>Dashboard</h1>
      
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '24px',
      }}>
        <div style={{
          backgroundColor: '#fff',
          padding: '24px',
          borderRadius: '8px',
          boxShadow: '0 4px 8px rgba(0,0,0,0.05)',
          border: '1px solid #eee',
        }}>
          <h2 style={{ color: '#333', marginBottom: '16px' }}>Quick Stats</h2>
          <p style={{ marginBottom: '8px' }}>Total Meetings: <strong style={{ color: 'var(--accent-color)' }}>150</strong></p>
          <p style={{ marginBottom: '8px' }}>Total Tweets Sent: <strong style={{ color: 'var(--accent-color)' }}>500</strong></p>
          <p>Average Meeting Duration: <strong style={{ color: 'var(--accent-color)' }}>30 mins</strong></p>
        </div>

        <div style={{
          backgroundColor: '#fff',
          padding: '24px',
          borderRadius: '8px',
          boxShadow: '0 4px 8px rgba(0,0,0,0.05)',
          border: '1px solid #eee',
        }}>
          <h2 style={{ color: '#333', marginBottom: '16px' }}>Recent Activity</h2>
          <ul>
            <li style={{ marginBottom: '12px', paddingBottom: '12px', borderBottom: '1px solid #eee' }}>Meeting with John Doe ended.</li>
            <li style={{ marginBottom: '12px', paddingBottom: '12px', borderBottom: '1px solid #eee' }}>Tweet about new feature sent.</li>
            <li style={{ paddingBottom: '12px', borderBottom: '1px solid #eee' }}>Scheduled meeting with Jane Smith.</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

DashboardPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default DashboardPage; 