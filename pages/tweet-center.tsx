// pages/tweet-center.tsx
import React from 'react';
import SidebarLayout from '../src/components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const TweetCenterPage: NextPageWithLayout = () => {
  return (
    <div style={{ padding: '24px' }}>
      <h1 style={{ color: 'var(--accent-color)', marginBottom: '24px' }}>Tweet Center</h1>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '24px',
      }}>
        <div style={{
          backgroundColor: '#fff',
          padding: '24px',
          borderRadius: '8px',
          boxShadow: '0 4px 8px rgba(0,0,0,0.05)',
          border: '1px solid #eee',
        }}>
          <h2 style={{ color: '#333', marginBottom: '16px' }}>Compose New Tweet</h2>
          <textarea
            placeholder="What's happening?"
            style={{
              width: '100%',
              minHeight: '100px',
              padding: '12px',
              borderRadius: '4px',
              border: '1px solid #ccc',
              marginBottom: '16px',
              fontFamily: 'inherit',
            }}
          ></textarea>
          <button style={{
            backgroundColor: 'var(--accent-color)',
            color: '#fff',
            padding: '10px 20px',
            borderRadius: '4px',
            border: 'none',
            cursor: 'pointer',
            fontSize: '16px',
          }}>Tweet</button>
        </div>

        <div style={{
          backgroundColor: '#fff',
          padding: '24px',
          borderRadius: '8px',
          boxShadow: '0 4px 8px rgba(0,0,0,0.05)',
          border: '1px solid #eee',
        }}>
          <h2 style={{ color: '#333', marginBottom: '16px' }}>Recent Tweets</h2>
          <ul>
            <li style={{ marginBottom: '12px', paddingBottom: '12px', borderBottom: '1px solid #eee' }}>Placeholder tweet 1...</li>
            <li style={{ marginBottom: '12px', paddingBottom: '12px', borderBottom: '1px solid #eee' }}>Placeholder tweet 2...</li>
            <li style={{ paddingBottom: '12px', borderBottom: '1px solid #eee' }}>Placeholder tweet 3...</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

TweetCenterPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default TweetCenterPage; 