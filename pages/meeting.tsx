'use client';

import React, { useEffect, useState } from 'react';
import VideoCallGrid from '../src/components/VideoCallGrid';
import MeetingControls from '../src/components/MeetingControls';
import Daily from '@daily-co/daily-js';
import { DailyProvider } from '@daily-co/daily-react';
import SidebarLayout from '../src/components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const MeetingPage: NextPageWithLayout = () => {
  // Bring back the state for controls and Exie interaction
  const [joined, setJoined] = useState(false);
  const [micOn, setMicOn] = useState(true);
  const [cameraOn, setCameraOn] = useState(true);
  const [exieLoading, setExieLoading] = useState(false);
  const [exieVideoUrl, setExieVideoUrl] = useState<string | null>(null); // Exie's video URL
  const [exieTranscript, setExieTranscript] = useState(''); // Exie's transcript
  const [roomUrl, setRoomUrl] = useState<string | null>(null);

  // Replace Agora client creation with Daily room creation/joining
  useEffect(() => {
    // In a real application, you would typically create a room server-side
    // using your Daily API key and get the room URL from your backend.
    // For this example, we\'ll construct a URL directly.
    const domain = 'YOUR_DAILY_DOMAIN'; // Replace with your Daily.co domain
    const roomName = 'my-exie-meeting'; // Replace with a desired room name
    const apiKey = '5bfd26c7ee79f9cc769ad87d9d7825306c3c568d055071a877d74217d4caa3cd'; // WARNING: Do not expose API key in frontend in production

    // This is a simplified approach; typically you\'d handle room creation/joining 
    // with more robust logic, potentially involving tokens for participants.
    const newRoomUrl = `https://${domain}.daily.co/${roomName}?apiKey=${apiKey}`; // Consider fetching this securely
    setRoomUrl(newRoomUrl);

    // Basic check if joined state should be true initially (e.g., if a room URL is set)
    // This might need refinement based on actual Daily.co connection status
    if (newRoomUrl) {
      setJoined(true); // Assume joined if we have a room URL
    }

    // Cleanup is handled by DailyProvider automatically on unmount
  }, []); // Run once on component mount

  // Handlers for controls - Implement the logic here
  const handleToggleMic = async () => {
    // Toggle mic logic - This will need to interact with the track inside VideoCallGrid
    // For now, we\'ll just update the state here. VideoCallGrid will use the prop.
    setMicOn(!micOn);
    // Actual track enabling/disabling will happen within VideoCallGrid\'s effects based on the micOn prop.
  };

  const handleToggleCamera = async () => {
    // Toggle camera logic - This will need to interact with the track inside VideoCallGrid
    // For now, we\'ll just update the state here. VideoCallGrid will use the prop.
    setCameraOn(!cameraOn);
    // Actual track enabling/disabling will happen within VideoCallGrid\'s effects based on the cameraOn prop.
  };

  const handleLeave = async () => {
    // Leave logic - This will need to trigger leaving the Agora channel in VideoCallGrid
    // For now, we\'ll just update the joined state.
    setJoined(false);
    // The actual leave operation will happen within VideoCallGrid based on the joined prop or a specific leave prop.
    window.location.reload(); // Simple reload to reset state for now
  };

   // Handle Talk to Exie logic (re-implemented here)
   const handleTalkToExie = async () => {
    if (!micOn || exieLoading) return; // Ensure mic state is on and not already loading
    setExieLoading(true);
    setExieTranscript(''); // Clear previous transcript
    setExieVideoUrl(null); // Clear previous video
    console.log('Starting audio recording...');

    let mediaRecorder: MediaRecorder | null = null;
    let stream: MediaStream | null = null;

    try {
       // Get a new audio stream specifically for recording
       stream = await navigator.mediaDevices.getUserMedia({ audio: true });
       mediaRecorder = new MediaRecorder(stream, { mimeType: 'audio/webm' });
       const audioChunks: Blob[] = [];

       mediaRecorder.ondataavailable = (e) => {
         if (e.data.size > 0) {
           audioChunks.push(e.data);
           console.log('Collected audio chunk:', e.data.size, 'bytes');
         }
       };

       mediaRecorder.onstop = async () => {
         console.log('Audio recording stopped. Total chunks:', audioChunks.length);
         const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
         console.log('Audio Blob created:', audioBlob.size, 'bytes', audioBlob.type);

         // Send to /api/transcribe
         console.log('Sending audio to /api/transcribe...');
         const res = await fetch('/api/transcribe', {
           method: 'POST',
           body: audioBlob,
           headers: { 'Content-Type': 'audio/webm' },
         });
         if (!res.ok) throw new Error(`Transcribe API error: ${res.status}`);
         const data = await res.json();
         const transcript = data.transcript || '';
         console.log('Transcript received:', transcript);
         setExieTranscript(transcript); // Update Exie\'s transcript state

         if (transcript) {
           // Send to AI pipeline (gpt -> voice -> video)
           console.log('Sending transcript to /api/gpt...');
           const gptRes = await fetch('/api/gpt', {
             method: 'POST',
             headers: { 'Content-Type': 'application/json' },
             body: JSON.stringify({ message: transcript, tweetsOrHandle: '' })
           });
           if (!gptRes.ok) throw new Error(`GPT API error: ${gptRes.status}`);
           const gptData = await gptRes.json();
           const mentorReply = gptData.reply || 'Sorry, I could not generate a response.';
           console.log('GPT Reply received:', mentorReply);
           setExieTranscript(mentorReply); // Update Exie\'s transcript state with reply

           // ElevenLabs
           console.log('Sending text to /api/voice...');
           const voiceRes = await fetch('/api/voice', {
             method: 'POST',
             headers: { 'Content-Type': 'application/json' },
             body: JSON.stringify({ text: mentorReply })
           });
           if (!voiceRes.ok) throw new Error(`Voice API error: ${voiceRes.status}`);
           const voiceData = await voiceRes.json();
           console.log('Voice data received (base64 length):', voiceData.audioBase64?.length);
           if (!voiceData.audioBase64) throw new Error('No audio returned from ElevenLabs.');

           // Create a blob URL for the audio
           const audioBlob = new Blob([Uint8Array.from(atob(voiceData.audioBase64), c => c.charCodeAt(0))], { type: 'audio/mpeg' });
           const audioUrl = URL.createObjectURL(audioBlob);
           console.log('Audio Blob URL created:', audioUrl);

           // D-ID
           console.log('Sending audio URL to /api/video...');
           const videoRes = await fetch('/api/video', {
             method: 'POST',
             headers: { 'Content-Type': 'application/json' },
             body: JSON.stringify({ audioUrl, imageUrl: 'https://via.placeholder.com/320x240.png?text=Exie+AI' }) // Use consistent placeholder
           });
           if (!videoRes.ok) throw new Error(`Video API error: ${videoRes.status}`);
           const videoData = await videoRes.json();
           console.log('Video URL received:', videoData.videoUrl);
           if (videoData.videoUrl) setExieVideoUrl(videoData.videoUrl); // Update Exie\'s video URL state

            // Play the audio
            const audio = new Audio(audioUrl);
            audio.play();

         } else {
            console.log('No transcript to process.');
         }
       };

       mediaRecorder.start();
       // Stop recording after a short delay (adjust as needed)
       setTimeout(() => {
         if (mediaRecorder?.state !== 'inactive') mediaRecorder?.stop();
         // Stop the getUserMedia tracks after recording stops
         stream?.getTracks().forEach(track => track.stop());
       }, 5000); // Record for 5 seconds max

    } catch (error) {
      console.error('Error recording audio or processing AI pipeline:', error);
      setExieTranscript('Sorry, there was an error processing your request.'); // Update Exie\'s transcript state on error
    } finally {
       setExieLoading(false);
    }
  };

  // Effect to join the Agora channel when component mounts and agoraClient is ready (now handled in VideoCallGrid)
  // However, we need to set \'joined\' state based on connection status.
  // We\'ll rely on VideoCallGrid to emit a \'joined\' status change or similar if needed, or manage joining entirely in VideoCallGrid\'s hook.
  // For simplicity now, assume VideoCallGrid\'s useJoin hook is sufficient to manage the connection, and we\'ll rely on the mic/camera tracks being ready to indicate a potential \'joined\' state for controls visibility.
  // A more robust approach might involve a context or callback from VideoCallGrid when truly connected.
  useEffect(() => {
    // Simple way to set joined state based on whether local tracks are available
    if (micOn && cameraOn) {
        // This is a simplification. A real \'joined\' status should come from Agora\'s connection state.
        // We will pass the \'joined\' prop down, and VideoCallGrid\'s useJoin will attempt to join.
        // We might need a mechanism to get the actual connection state back up from VideoCallGrid.
        // For now, let\'s rely on VideoCallGrid\'s internal joined state derived from the Agora client listener
        // and pass down the mic/camera state and handlers.
        // The \'joined\' state in MeetingPage will primarily control visibility of the footer controls.

     // Re-add connection state listener if not fully handled by VideoCallGrid
     // If VideoCallGrid\'s useJoin manages connection and updates an internal state,
     // we might need a way for VideoCallGrid to communicate the joined status up.
     // Let\'s stick to the current plan: VideoCallGrid handles Agora, MeetingPage handles overall UI and AI pipeline.
     // MeetingPage\'s \'joined\' state will primarily control visibility of the footer controls.

     // A potential approach: VideoCallGrid calls a prop function like onJoinedStatusChange(status: boolean)
     // useEffect(() => {
     //   if (agoraClient) {
     //     agoraClient.on(\'connection-state-change\', (state) => {
     //       if (state === \'CONNECTED\') {
     //         setJoined(true);
     //       } else {
     //         setJoined(false);
     //       }
     //     });
     //   }
     // }, [agoraClient]);

     // For Daily.co, the useRoom hook\'s state might be a better indicator of joined status.
     // Let\'s update the joined state based on the presence of a room URL for now, as done in the initial effect.
    }

  }, [micOn, cameraOn]); // Depend on micOn and cameraOn to re-check joined status (simplified)





  // Render the UI only if roomUrl is available (meaning we are attempting to join or are in a room)
  if (!roomUrl) {
    return <div>Loading meeting...</div>; // Or a proper loading spinner/page
  }

  return (
    // Wrap the meeting content in DailyProvider to provide the Daily room context to children
    <DailyProvider
      url={roomUrl}
      // You might need to configure token or other properties here
    >
       <div style={{ height: '100vh', display: 'flex', flexDirection: 'column', backgroundColor: '#f8f8f8' }}> {/* Added a light background to the meeting container */}
        {/* Video grid takes up most of the space */}
        <VideoCallGrid
          joined={joined} // Pass joined state down
          micOn={micOn} // Pass mic state down
          cameraOn={cameraOn} // Pass camera state down
          exieLoading={exieLoading} // Pass Exie loading state down
          exieVideoUrl={exieVideoUrl} // Pass Exie video URL down
          exieTranscript={exieTranscript} // Pass Exie transcript down
          onToggleMic={handleToggleMic} // Pass handler down
          onToggleCamera={handleToggleCamera} // Pass handler down
          onLeave={handleLeave} // Pass handler down
          onTalkToExie={handleTalkToExie} // Pass handler down
        />

        {/* Meeting controls in a footer */}
        {/* Only show controls if joined (simplified) */}
        {joined && (
          <MeetingControls
            micOn={micOn}
            cameraOn={cameraOn}
            onToggleMic={handleToggleMic}
            onToggleCamera={handleToggleCamera}
            onLeave={handleLeave}
            onTalkToExie={handleTalkToExie} // Pass Exie handler
            exieLoading={exieLoading} // Pass Exie loading state
          />
        )}
      </div>
    </DailyProvider>
  );
};

MeetingPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default MeetingPage; 